#!/usr/bin/env python3
"""
Gemini Vision MCP Server - Uses Google Gemini API for vision/OCR tasks
Provides screenshot + OCR capabilities for automation
"""

import os
import sys
import subprocess
import tempfile
from typing import Dict, Any, Optional
from pathlib import Path
import google.generativeai as genai
from mcp.server.fastmcp import FastMCP, Image

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Configure Gemini
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')
if not GEMINI_API_KEY:
    raise ValueError("GEMINI_API_KEY not found in environment variables")

genai.configure(api_key=GEMINI_API_KEY)

# Create FastMCP server
mcp = FastMCP("GeminiVisionMCP")

def log(message: str) -> None:
    """Log a message to stderr."""
    print(f"GEMINI-MCP: {message}", file=sys.stderr)

@mcp.tool()
def take_screenshot_and_analyze(prompt: str = "Extract all text from this screenshot") -> str:
    """Take a screenshot and analyze it with Gemini Vision."""
    try:
        # Take screenshot using macOS screencapture
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
            screenshot_path = tmp_file.name
        
        # Capture screenshot
        result = subprocess.run(['screencapture', '-x', screenshot_path], 
                              capture_output=True, text=True)
        
        if result.returncode != 0:
            return f"Error taking screenshot: {result.stderr}"
        
        if not Path(screenshot_path).exists():
            return "Error: Screenshot file was not created"
        
        # Analyze with Gemini
        response = analyze_image(screenshot_path, prompt)
        
        # Cleanup
        try:
            os.unlink(screenshot_path)
        except:
            pass
            
        return response
        
    except Exception as e:
        return f"Error in screenshot and analysis: {str(e)}"

@mcp.tool()
def analyze_image(image_path: str, prompt: str = "Describe what you see in this image") -> str:
    """Analyze an image using Gemini Vision API."""
    try:
        # Check if file exists
        if not Path(image_path).exists():
            return f"Error: Image file not found at {image_path}"
        
        log(f"Analyzing image: {image_path}")
        
        # Initialize Gemini model
        model = genai.GenerativeModel('gemini-1.5-flash')
        
        # Load image
        with open(image_path, 'rb') as img_file:
            image_data = img_file.read()
        
        # Create image part for Gemini
        image_part = {
            "mime_type": "image/png",
            "data": image_data
        }
        
        # Generate response
        response = model.generate_content([prompt, image_part])
        
        if response.text:
            log(f"Analysis complete, response length: {len(response.text)}")
            return response.text
        else:
            return "No response generated from Gemini"
        
    except Exception as e:
        log(f"Error in analyze_image: {str(e)}")
        return f"Error analyzing image: {str(e)}"

@mcp.tool()
def extract_text_for_typing(image_path: str) -> str:
    """Extract text that needs to be typed from a screenshot (optimized for MonkeyType)."""
    prompt = """Look at this screenshot and extract ALL the text that needs to be typed.
    This is likely from a typing test website like MonkeyType.

    EXTRACT COMPLETE TEXT RULES:
    1. Return ALL the text that needs to be typed - the ENTIRE passage/paragraph
    2. Include ALL lines, sentences, and paragraphs that appear in greyish/untyped color
    3. Do not include UI elements, buttons, or interface text
    4. Do not include already typed text (usually in white/highlighted differently)
    5. Preserve exact spacing, punctuation, and line breaks
    6. If you see a cursor or highlight, extract ALL text after that position
    7. Extract the complete multi-line text block, not just a few words
    8. Include every single character, space, and punctuation mark exactly as shown
    9. Maintain original formatting including paragraph breaks if present

    Return the COMPLETE raw text to type (all lines/paragraphs):"""

    return analyze_image(image_path, prompt)

@mcp.tool()
def find_typing_area(image_path: str) -> str:
    """Find the typing area coordinates in a screenshot."""
    prompt = """Analyze this screenshot to find the typing area or text input field.
    
    Look for:
    1. Text input areas or typing zones
    2. Cursor position or text focus indicators
    3. Areas where text appears to be entered
    
    Provide the approximate coordinates (x, y) of the center of the typing area.
    Format: "Typing area center: (x, y)" followed by any relevant details."""
    
    return analyze_image(image_path, prompt)

@mcp.tool()
def detect_typing_errors(image_path: str) -> str:
    """Detect typing errors or mistakes in a screenshot."""
    prompt = """Look at this screenshot from a typing test and identify:
    
    1. Any red highlighting indicating typing errors
    2. Incorrect characters or words
    3. Current typing accuracy status
    4. Whether there are any error indicators
    
    Return: "ERRORS: [description]" or "NO_ERRORS" if everything looks correct."""
    
    return analyze_image(image_path, prompt)

@mcp.tool()
def get_typing_stats(image_path: str) -> str:
    """Extract typing statistics from a screenshot (WPM, accuracy, etc.)."""
    prompt = """Extract typing performance statistics from this screenshot:
    
    Look for:
    1. WPM (Words Per Minute)
    2. Accuracy percentage
    3. Time remaining or elapsed
    4. Any other performance metrics
    
    Format the response as:
    WPM: [number]
    Accuracy: [percentage]
    Time: [time info]
    Other: [any other stats]"""
    
    return analyze_image(image_path, prompt)

@mcp.tool()
def analyze_monkeytype_screen(image_path: str) -> str:
    """Comprehensive analysis of MonkeyType screen for automation."""
    prompt = """Analyze this MonkeyType screenshot for automation purposes:

    1. COMPLETE TEXT TO TYPE: Extract ALL the text that needs to be typed - the entire passage/paragraph visible in greyish color
    2. TYPING PROGRESS: What has been typed vs what remains
    3. ERRORS: Any red highlighting or mistakes visible
    4. STATS: Current WPM, accuracy, time if visible
    5. STATUS: Is the test active, completed, or waiting to start?

    CRITICAL: For TEXT_TO_TYPE, extract the COMPLETE multi-line text block, not just a few words.
    Include every sentence, paragraph, and line that appears untyped (greyish).
    Preserve exact spacing, punctuation, and formatting.

    Format response as:
    TEXT_TO_TYPE: [COMPLETE exact text - all lines/paragraphs]
    PROGRESS: [status]
    ERRORS: [any errors]
    STATS: [current stats]
    STATUS: [test status]"""

    return analyze_image(image_path, prompt)

def main():
    """Main entry point for the MCP server."""
    try:
        log("Starting Gemini Vision MCP server...")
        mcp.run()
    except KeyboardInterrupt:
        log("Server shutting down...")
    except Exception as e:
        log(f"Error: {str(e)}")

if __name__ == "__main__":
    main()
